// 备忘录与待办功能自测脚本
// 在浏览器控制台中运行此脚本来测试功能

console.log('🧪 开始备忘录与待办功能自测...');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:8081/api/v1',
  token: localStorage.getItem('authToken') || localStorage.getItem('auth_token'),
  testMemo: {
    content: '测试备忘录内容 - ' + new Date().toISOString(),
    tags: ['测试', '自动化'],
    reminder_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 明天
  },
  testTodo: {
    title: '测试待办事项 - ' + new Date().toISOString(),
    due_date: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(), // 后天
    priority: 'medium'
  }
};

// 辅助函数
const apiCall = async (method, endpoint, data = null) => {
  const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${TEST_CONFIG.token}`
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${JSON.stringify(result)}`);
    }
    
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// 测试结果收集
const testResults = {
  memo: { create: false, read: false, update: false, delete: false },
  todo: { create: false, read: false, update: false, delete: false, toggle: false }
};

let createdMemoId = null;
let createdTodoId = null;

// 备忘录测试
const testMemos = async () => {
  console.log('📝 测试备忘录功能...');
  
  // 1. 创建备忘录
  console.log('1. 创建备忘录...');
  const createResult = await apiCall('POST', '/memos', TEST_CONFIG.testMemo);
  if (createResult.success) {
    createdMemoId = createResult.data.id;
    testResults.memo.create = true;
    console.log('✅ 备忘录创建成功:', createdMemoId);
  } else {
    console.error('❌ 备忘录创建失败:', createResult.error);
    return;
  }
  
  // 2. 读取备忘录列表
  console.log('2. 读取备忘录列表...');
  const readResult = await apiCall('GET', '/memos');
  if (readResult.success && Array.isArray(readResult.data)) {
    testResults.memo.read = true;
    console.log('✅ 备忘录列表读取成功，数量:', readResult.data.length);
  } else {
    console.error('❌ 备忘录列表读取失败:', readResult.error);
  }
  
  // 3. 更新备忘录
  console.log('3. 更新备忘录...');
  const updateData = {
    content: TEST_CONFIG.testMemo.content + ' (已更新)',
    tags: ['测试', '更新']
  };
  const updateResult = await apiCall('PUT', `/memos/${createdMemoId}`, updateData);
  if (updateResult.success) {
    testResults.memo.update = true;
    console.log('✅ 备忘录更新成功');
  } else {
    console.error('❌ 备忘录更新失败:', updateResult.error);
  }
  
  // 4. 删除备忘录
  console.log('4. 删除备忘录...');
  const deleteResult = await apiCall('DELETE', `/memos/${createdMemoId}`);
  if (deleteResult.success) {
    testResults.memo.delete = true;
    console.log('✅ 备忘录删除成功');
  } else {
    console.error('❌ 备忘录删除失败:', deleteResult.error);
  }
};

// 待办事项测试
const testTodos = async () => {
  console.log('📋 测试待办事项功能...');
  
  // 1. 创建待办
  console.log('1. 创建待办事项...');
  const createResult = await apiCall('POST', '/todos', TEST_CONFIG.testTodo);
  if (createResult.success) {
    createdTodoId = createResult.data.id;
    testResults.todo.create = true;
    console.log('✅ 待办事项创建成功:', createdTodoId);
  } else {
    console.error('❌ 待办事项创建失败:', createResult.error);
    return;
  }
  
  // 2. 读取待办列表
  console.log('2. 读取待办事项列表...');
  const readResult = await apiCall('GET', '/todos');
  if (readResult.success && Array.isArray(readResult.data)) {
    testResults.todo.read = true;
    console.log('✅ 待办事项列表读取成功，数量:', readResult.data.length);
  } else {
    console.error('❌ 待办事项列表读取失败:', readResult.error);
  }
  
  // 3. 更新待办
  console.log('3. 更新待办事项...');
  const updateData = {
    title: TEST_CONFIG.testTodo.title + ' (已更新)',
    priority: 'high'
  };
  const updateResult = await apiCall('PUT', `/todos/${createdTodoId}`, updateData);
  if (updateResult.success) {
    testResults.todo.update = true;
    console.log('✅ 待办事项更新成功');
  } else {
    console.error('❌ 待办事项更新失败:', updateResult.error);
  }
  
  // 4. 切换完成状态
  console.log('4. 切换待办事项完成状态...');
  const toggleResult = await apiCall('PUT', `/todos/${createdTodoId}`, { completed: true });
  if (toggleResult.success) {
    testResults.todo.toggle = true;
    console.log('✅ 待办事项状态切换成功');
  } else {
    console.error('❌ 待办事项状态切换失败:', toggleResult.error);
  }
  
  // 5. 删除待办
  console.log('5. 删除待办事项...');
  const deleteResult = await apiCall('DELETE', `/todos/${createdTodoId}`);
  if (deleteResult.success) {
    testResults.todo.delete = true;
    console.log('✅ 待办事项删除成功');
  } else {
    console.error('❌ 待办事项删除失败:', deleteResult.error);
  }
};

// 运行所有测试
const runAllTests = async () => {
  if (!TEST_CONFIG.token) {
    console.error('❌ 未找到认证令牌，请先登录');
    return;
  }
  
  console.log('🚀 开始运行所有测试...');
  
  await testMemos();
  await testTodos();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('备忘录功能:');
  console.log(`  创建: ${testResults.memo.create ? '✅' : '❌'}`);
  console.log(`  读取: ${testResults.memo.read ? '✅' : '❌'}`);
  console.log(`  更新: ${testResults.memo.update ? '✅' : '❌'}`);
  console.log(`  删除: ${testResults.memo.delete ? '✅' : '❌'}`);
  
  console.log('待办事项功能:');
  console.log(`  创建: ${testResults.todo.create ? '✅' : '❌'}`);
  console.log(`  读取: ${testResults.todo.read ? '✅' : '❌'}`);
  console.log(`  更新: ${testResults.todo.update ? '✅' : '❌'}`);
  console.log(`  状态切换: ${testResults.todo.toggle ? '✅' : '❌'}`);
  console.log(`  删除: ${testResults.todo.delete ? '✅' : '❌'}`);
  
  // 计算总体成功率
  const totalTests = Object.values(testResults.memo).length + Object.values(testResults.todo).length;
  const passedTests = Object.values(testResults.memo).filter(Boolean).length + 
                     Object.values(testResults.todo).filter(Boolean).length;
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  
  console.log(`\n🎯 总体成功率: ${passedTests}/${totalTests} (${successRate}%)`);
  
  if (successRate === '100.0') {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
};

// 启动测试
runAllTests().catch(error => {
  console.error('💥 测试运行出错:', error);
});
