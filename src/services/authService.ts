import { getBackendConfig } from '@/config/backend';

// Go 后端 API 客户端获取函数
async function getGoApiClient() {
  const config = getBackendConfig();
  const baseURL = config.goBackend?.baseUrl;

  return {
    async get(path: string, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('auth_token');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    },

    async post(path: string, data?: unknown, options: { headers?: Record<string, string> } = {}) {
      const token = localStorage.getItem('auth_token');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      };

      const response = await fetch(`${baseURL}${path}`, {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    }
  };
}

// 用户信息接口
export interface User {
  id: string | number;
  username?: string;
  email: string;
  email_verified?: boolean;
  avatar_url?: string;
  is_super_admin?: boolean;
  roles?: string[];
  created_at?: string;
  updated_at?: string;
}

// 认证响应接口
export interface AuthResponse {
  user: User;
  token?: string;
  session?: unknown;
}

// 登录请求
export interface LoginRequest {
  email: string;
  password: string;
}

// 注册请求
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// OAuth提供商
export interface OAuthProvider {
  id: string;
  provider_name: string;
  client_id: string;
  redirect_url: string;
  scopes: string[];
  enabled: boolean;
}

// 登录
export const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
  try {
    const apiClient = await getGoApiClient();
    const response = await apiClient.post('/auth/login', credentials);

    // 保存token到localStorage
    if (response.token) {
      localStorage.setItem('auth_token', response.token);
    }

    return {
      user: response.user,
      token: response.token,
    };
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// 注册
export const register = async (userData: RegisterRequest): Promise<AuthResponse> => {
  try {
    const apiClient = await getGoApiClient();
    const response = await apiClient.post('/auth/register', userData);

    // 保存token到localStorage
    if (response.token) {
      localStorage.setItem('auth_token', response.token);
    }

    return {
      user: response.user,
      token: response.token,
    };
  } catch (error) {
    console.error('Register error:', error);
    throw error;
  }
};

// 登出
export const logout = async (): Promise<void> => {
  try {
    // 调用Go后端登出API
    const apiClient = await getGoApiClient();
    await apiClient.post('/auth/logout');
    
    // 清除本地存储的token
    localStorage.removeItem('auth_token');
    localStorage.removeItem('authToken'); // 兼容性
  } catch (error) {
    console.error('Logout error:', error);
    // 即使API调用失败，也要清除本地token
    localStorage.removeItem('auth_token');
    localStorage.removeItem('authToken');
    throw error;
  }
};

// 获取当前用户
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return null;
    }

    const apiClient = await getGoApiClient();
    const response = await apiClient.get('/auth/me');

    // 后端直接返回用户信息，不是包装在user字段中
    console.log('Raw response from /auth/me:', response);

    // 确保返回的数据符合User接口
    const userData: User = {
      id: response.id,
      username: response.username,
      email: response.email,
      email_verified: response.email_verified,
      avatar_url: response.avatar_url,
      is_super_admin: response.is_super_admin,
      roles: response.roles || [],
      created_at: response.created_at,
      updated_at: response.updated_at
    };

    console.log('Processed user data:', userData);
    return userData;
  } catch (error) {
    console.error('Get current user error:', error);
    // 如果token无效，清除本地存储
    localStorage.removeItem('auth_token');
    localStorage.removeItem('authToken');
    return null;
  }
};

// 获取OAuth提供商列表
export const getOAuthProviders = async (): Promise<OAuthProvider[]> => {
  try {
    const apiClient = await getGoApiClient();
    const response = await apiClient.get('/auth/oauth/providers');
    return response.providers || [];
  } catch (error) {
    console.error('Get OAuth providers error:', error);
    return [];
  }
};

// 发送验证邮件
export const sendVerificationEmail = async (): Promise<void> => {
  try {
    const apiClient = await getGoApiClient();
    await apiClient.post('/auth/verify/send');
  } catch (error) {
    console.error('Send verification email error:', error);
    throw error;
  }
};

// 验证邮箱
export const verifyEmail = async (token: string): Promise<void> => {
  try {
    const apiClient = await getGoApiClient();
    await apiClient.post('/auth/verify', { token });
  } catch (error) {
    console.error('Verify email error:', error);
    throw error;
  }
};

// 忘记密码
export const forgotPassword = async (email: string): Promise<void> => {
  try {
    const apiClient = await getGoApiClient();
    await apiClient.post('/auth/forgot-password', { email });
  } catch (error) {
    console.error('Forgot password error:', error);
    throw error;
  }
};

// 重置密码
export const resetPassword = async (token: string, newPassword: string): Promise<void> => {
  try {
    const apiClient = await getGoApiClient();
    await apiClient.post('/auth/reset-password', { 
      token, 
      password: newPassword 
    });
  } catch (error) {
    console.error('Reset password error:', error);
    throw error;
  }
};