import { getBackendConfig } from '@/config/backend';
import apiClient from './api';

export interface DomainWhitelist {
  id: string;
  domain: string;
  approved: boolean;
  user_id?: number;
  created_at: string;
}

export interface CreateDomainRequest {
  domain: string;
  approved?: boolean;
}

export interface CreateDomainResponse {
  domain: DomainWhitelist;
  message: string;
  approved: boolean;
}

// 获取域名白名单（无需认证，但只返回已审核的域名，管理员可看到全部）
export async function getDomainWhitelist(params?: {
  approved?: string;
  search?: string;
  page?: number;
  limit?: number;
}): Promise<{
  domains: DomainWhitelist[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  is_admin: boolean;
}> {
  try {
    const searchParams = new URLSearchParams();
    if (params?.approved) searchParams.append('approved', params.approved);
    if (params?.search) searchParams.append('search', params.search);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const response = await apiClient.get(`/domains/whitelist?${searchParams.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching domain whitelist:', error);
    throw error;
  }
}

// 添加缓存机制避免重复请求
let approvedDomainsCache: string[] | null = null;
let lastDomainsFetchTime = 0;
const DOMAINS_CACHE_DURATION = 60000; // 60秒缓存

// 获取已批准的域名列表（用于旧版兼容）
export async function getApprovedDomains(): Promise<string[]> {
  try {
    // 检查缓存
    const now = Date.now();
    if (approvedDomainsCache && (now - lastDomainsFetchTime) < DOMAINS_CACHE_DURATION) {
      console.log('使用缓存的域名白名单数据');
      return approvedDomainsCache;
    }

    console.log('获取域名白名单数据...');
    const response = await getDomainWhitelist({ approved: 'true' });
    const domains = response.domains.map(d => d.domain);

    // 更新缓存
    approvedDomainsCache = domains;
    lastDomainsFetchTime = now;

    return domains;
  } catch (error) {
    console.error('Error fetching approved domains:', error);
    return [];
  }
}

// 清理缓存的函数
export function clearDomainsCache() {
  approvedDomainsCache = null;
  lastDomainsFetchTime = 0;
}

// 添加域名到白名单（需要认证）
export async function createDomainWhitelist(domain: string): Promise<CreateDomainResponse> {
  try {
    const response = await apiClient.post('/domains/whitelist', { domain });
    // 清理缓存，因为数据可能已更新
    clearDomainsCache();
    return response.data;
  } catch (error) {
    console.error('Error creating domain whitelist:', error);
    throw error;
  }
}

// 更新域名白名单（仅管理员）
export async function updateDomainWhitelist(id: string, data: Partial<CreateDomainRequest>): Promise<DomainWhitelist> {
  try {
    const response = await apiClient.put(`/domains/whitelist/${id}`, data);
    // 清理缓存，因为数据可能已更新
    clearDomainsCache();
    return response.data;
  } catch (error) {
    console.error('Error updating domain whitelist:', error);
    throw error;
  }
}

// 删除域名白名单（仅管理员）
export async function deleteDomainWhitelist(id: string): Promise<void> {
  try {
    await apiClient.delete(`/domains/whitelist/${id}`);
    // 清理缓存，因为数据可能已更新
    clearDomainsCache();
  } catch (error) {
    console.error('Error deleting domain whitelist:', error);
    throw error;
  }
}
