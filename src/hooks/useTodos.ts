import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import { getBackendConfig } from '@/config/backend';

export interface Todo {
  id: string;
  title: string;
  completed: boolean;
  created_at: string;
  due_date?: string | null;
  priority?: 'low' | 'medium' | 'high' | null;
}

export const useTodos = (userId?: string | number) => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(true);
  const { language } = useAppContext();
  const { toast } = useToast();
  const { user } = useAppContext();

  const fetchTodos = useCallback(async () => {
    if (!user) {
      setLoading(false);
      setTodos([]);
      return;
    }

    try {
      setLoading(true);
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      const response = await fetch(`${baseURL}/todos`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const typedTodos = data?.map((todo: any) => ({
        ...todo,
        priority: todo.priority as 'low' | 'medium' | 'high' | null
      })) || [];

      setTodos(typedTodos);
    } catch (error) {
      console.error('Error fetching todos:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '加载待办事项失败' : 'Failed to load todos'
      });
      setTodos([]);
    } finally {
      setLoading(false);
    }
  }, [user, language, toast]);

  useEffect(() => {
    let isMounted = true;

    if (user && isMounted) {
      fetchTodos();
    } else if (isMounted) {
      setLoading(false);
      setTodos([]);
    }

    return () => {
      isMounted = false;
    };
  }, [user, fetchTodos]);

  const addTodo = async (title: string, dueDate?: string, priority?: 'low' | 'medium' | 'high') => {
    if (!user) {
      toast({
        variant: "destructive",
        description: language === 'zh' ? '您必须登录才能添加待办事项' : 'You must be logged in to add todos'
      });
      return;
    }

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      const newTodo = {
        title,
        due_date: dueDate || null,
        priority: priority || null,
      };

      const response = await fetch(`${baseURL}/todos`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(newTodo),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const typedTodo: Todo = {
        ...data,
        priority: data.priority as 'low' | 'medium' | 'high' | null
      };

      setTodos([typedTodo, ...todos]);
      toast({
        description: language === 'zh' ? '待办事项已添加' : 'Todo added successfully'
      });
    } catch (error) {
      console.error('Error adding todo:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '添加待办事项失败' : 'Failed to add todo'
      });
    }
  };

  const toggleTodo = async (id: string, completed: boolean) => {
    if (!user) return;

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      const response = await fetch(`${baseURL}/todos/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ completed }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setTodos(todos.map(todo =>
        todo.id === id ? { ...todo, completed } : todo
      ));
    } catch (error) {
      console.error('Error updating todo:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '更新待办事项失败' : 'Failed to update todo'
      });
    }
  };

  const updateTodo = async (id: string, updates: Partial<Omit<Todo, 'id' | 'created_at'>>) => {
    if (!user) return;

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      const response = await fetch(`${baseURL}/todos/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setTodos(todos.map(todo =>
        todo.id === id ? { ...todo, ...updates } : todo
      ));

      toast({
        description: language === 'zh' ? '待办事项已更新' : 'Todo updated successfully'
      });
    } catch (error) {
      console.error('Error updating todo:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '更新待办事项失败' : 'Failed to update todo'
      });
    }
  };

  const deleteTodo = async (id: string) => {
    if (!user) return;

    try {
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken');

      const response = await fetch(`${baseURL}/todos/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setTodos(todos.filter(todo => todo.id !== id));
      toast({
        description: language === 'zh' ? '待办事项已删除' : 'Todo deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting todo:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? '删除待办事项失败' : 'Failed to delete todo'
      });
    }
  };

  const refreshTodos = useCallback(() => {
    if (user) {
      fetchTodos();
    }
  }, [user, fetchTodos]);

  return {
    todos,
    loading,
    addTodo,
    toggleTodo,
    updateTodo,
    deleteTodo,
    refreshTodos
  };
};
