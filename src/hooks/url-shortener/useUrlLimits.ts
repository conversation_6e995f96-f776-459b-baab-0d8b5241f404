import { useState, useEffect } from 'react';
import apiClient from '@/services/api';

interface UrlLimits {
  dailyLimit: number;
  currentCount: number;
  remainingUrls: number;
  isLimitReached: boolean;
}

// 添加缓存机制避免重复请求
let limitsCache: UrlLimits | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 30000; // 30秒缓存

export const useUrlLimits = () => {
  const [limits, setLimits] = useState<UrlLimits>({
    dailyLimit: 10,
    currentCount: 0,
    remainingUrls: 10,
    isLimitReached: false
  });
  const [isLoading, setIsLoading] = useState(true);

  const fetchLimits = async (forceRefresh = false) => {
    try {
      setIsLoading(true);

      // 检查缓存
      const now = Date.now();
      if (!forceRefresh && limitsCache && (now - lastFetchTime) < CACHE_DURATION) {
        console.log('使用缓存的URL限制数据');
        setLimits(limitsCache);
        setIsLoading(false);
        return;
      }

      // 使用 Go 后端
      try {
        console.log('获取URL限制数据...');
        const response = await apiClient.get('/url-limits');
        const data = response.data;

        if (data) {
          const newLimits = {
            dailyLimit: data.daily_limit || 10,
            currentCount: data.current_count || 0,
            remainingUrls: data.remaining || 10,
            isLimitReached: data.is_limited || false
          };
          setLimits(newLimits);
          // 更新缓存
          limitsCache = newLimits;
          lastFetchTime = now;
        }
      } catch (error) {
        console.error('Error fetching URL limits from Go backend:', error);
        // 使用默认值
        const defaultLimits = {
          dailyLimit: 10,
          currentCount: 0,
          remainingUrls: 10,
          isLimitReached: false
        };
        setLimits(defaultLimits);
        limitsCache = defaultLimits;
        lastFetchTime = now;
      }
    } catch (error) {
      console.error('Error fetching URL limits:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkUrlCount = async (): Promise<number> => {
    try {
      // 使用 Go 后端
      const response = await apiClient.get('/url-count');
      return response.data?.count || 0;
    } catch (error) {
      console.error('Error checking URL count:', error);
      return 0;
    }
  };

  useEffect(() => {
    fetchLimits();
  }, []);

  return {
    limits,
    isLoading,
    fetchLimits,
    checkUrlCount
  };
};
