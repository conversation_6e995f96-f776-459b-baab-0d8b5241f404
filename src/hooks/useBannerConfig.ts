import { useState, useEffect } from 'react';
import { fetchBannerConfig } from '@/services/bannerConfigService';

export type DisplayStyle = 'default' | 'stacked' | 'inline' | 'flow';

export interface BannerTextLine {
  text: string;
  gradient: string;
}

export interface BannerConfig {
  id?: string;
  use_third_line?: boolean;
  first_text?: string;
  first_gradient?: string;
  second_text?: string;
  second_gradient?: string;
  third_text?: string;
  third_gradient?: string;
  fourth_text?: string;
  fourth_gradient?: string;
  height?: number;
  spacing?: number;
  animation_speed?: number;
  custom_class?: string;
  display_style?: DisplayStyle;
  lines?: BannerTextLine[];
  created_at?: string;
  updated_at?: string;
  // Main title and description fields
  main_title?: { en: string; zh: string };
  main_description?: { en: string; zh: string };
  // Grid layout configuration
  grid_rows?: number;
  grid_columns?: number;
}

// Default configuration values
export const DEFAULT_BANNER_CONFIG: BannerConfig = {
  first_text: 'All-in-One Toolbox',
  first_gradient: 'from-primary to-brand-500',
  second_text: 'Boost Your Productivity',
  second_gradient: 'from-accent1-400 to-brand-500',
  third_text: '',
  third_gradient: '',
  fourth_text: '',
  fourth_gradient: '',
  use_third_line: false,
  height: 250,
  spacing: 2,
  animation_speed: 0.8,
  custom_class: '',
  display_style: 'default',
  lines: [],
  main_title: { en: 'All-in-One Toolbox', zh: '一站式工具集' },
  main_description: { en: 'Boost your productivity', zh: '提升工作效率' },
  grid_rows: 1,
  grid_columns: 1
};

// Helper function to parse banner text lines from JSON
export const parseBannerTextLines = (lines: unknown): BannerTextLine[] => {
  if (!lines) return [];
  if (Array.isArray(lines)) {
    return lines.map(line => ({
      text: line.text || '',
      gradient: line.gradient || 'from-primary to-brand-500'
    }));
  }
  // If it's a string (from JSON), try to parse it
  if (typeof lines === 'string') {
    try {
      const parsed = JSON.parse(lines);
      if (Array.isArray(parsed)) {
        return parsed.map(line => ({
          text: line.text || '',
          gradient: line.gradient || 'from-primary to-brand-500'
        }));
      }
    } catch (e) {
      console.warn('Failed to parse lines as JSON:', e);
    }
  }
  return [];
};

// 添加缓存机制避免重复请求
let bannerConfigCache: BannerConfig | null = null;
let lastBannerFetchTime = 0;
const BANNER_CACHE_DURATION = 60000; // 60秒缓存

export const useBannerConfig = () => {
  const [bannerConfig, setBannerConfig] = useState<BannerConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);

        // 检查缓存
        const now = Date.now();
        if (bannerConfigCache && (now - lastBannerFetchTime) < BANNER_CACHE_DURATION) {
          console.log('使用缓存的Banner配置数据');
          setBannerConfig(bannerConfigCache);
          setLoading(false);
          return;
        }

        console.log('获取Banner配置数据...');
        const data = await fetchBannerConfig();

        if (data) {
          // Parse any JSON fields like lines
          const config: BannerConfig = {
            ...data,
            display_style: 'default' as DisplayStyle,
            lines: []
          };
          
          // If there are no custom lines but we have traditional fields, convert them
          if ((!config.lines || config.lines.length === 0) && config.first_text) {
            config.lines = [];
            if (config.first_text) {
              config.lines.push({
                text: config.first_text,
                gradient: config.first_gradient || 'from-primary to-brand-500'
              });
            }
            if (config.second_text) {
              config.lines.push({
                text: config.second_text,
                gradient: config.second_gradient || 'from-accent1-400 to-brand-500'
              });
            }
            if (config.use_third_line && config.third_text) {
              config.lines.push({
                text: config.third_text,
                gradient: config.third_gradient || 'from-accent1-500 to-primary'
              });
            }
            if (config.use_third_line && config.fourth_text) {
              config.lines.push({
                text: config.fourth_text,
                gradient: config.fourth_gradient || 'from-brand-500 to-accent1-500'
              });
            }
          }

          setBannerConfig(config);
          // 更新缓存
          bannerConfigCache = config;
          lastBannerFetchTime = now;
        } else {
          const defaultConfig = DEFAULT_BANNER_CONFIG;
          setBannerConfig(defaultConfig);
          bannerConfigCache = defaultConfig;
          lastBannerFetchTime = now;
        }
      } catch (err) {
        console.error('Error fetching banner config:', err);
        setError(err as Error);
        const defaultConfig = DEFAULT_BANNER_CONFIG;
        setBannerConfig(defaultConfig);
        bannerConfigCache = defaultConfig;
        lastBannerFetchTime = now;
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  return {
    bannerConfig,
    loading,
    error,
    setBannerConfig
  };
};
