
import { useState, useEffect, useCallback } from 'react';
import { fetchPlatforms, fetchNews, fallbackPlatforms } from './api';
import { NewsPlatform, NewsItem, HotNewsHookReturn } from './types';
import { useAppContext } from '@/context/AppContext';

export const useHotNews = (): HotNewsHookReturn => {
  const { user, isAuthReady } = useAppContext();
  const [platforms, setPlatforms] = useState<NewsPlatform[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoadingPlatforms, setIsLoadingPlatforms] = useState(false);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available platforms
  const refetchPlatforms = useCallback(async () => {
    setIsLoadingPlatforms(true);
    setError(null);
    
    try {
      const data = await fetchPlatforms();
      setPlatforms(data);
      
      // Set the first platform as selected by default
      if (data.length > 0 && !selectedPlatform) {
        setSelectedPlatform(data[0].path);
      }
    } catch (err) {
      console.error('Error in refetchPlatforms:', err);
      setPlatforms(fallbackPlatforms);
      if (!selectedPlatform) {
        setSelectedPlatform(fallbackPlatforms[0].path);
      }
      setError('Failed to fetch platforms. Using local fallback data.');
    } finally {
      setIsLoadingPlatforms(false);
    }
  }, [selectedPlatform]);

  // Fetch news for the selected platform
  const refetchNews = useCallback(async () => {
    if (!selectedPlatform) return;
    
    setIsLoadingNews(true);
    setError(null);
    
    try {
      const data = await fetchNews(selectedPlatform);
      setNews(data);
    } catch (err) {
      console.error('Error in refetchNews:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch news. Please try again later.');
      setNews([]);
    } finally {
      setIsLoadingNews(false);
    }
  }, [selectedPlatform]);

  // 计算认证状态
  const isAuthenticated = isAuthReady && user !== null;

  // Fetch platforms on mount - 只有在用户登录时才执行
  useEffect(() => {
    if (isAuthenticated) {
      console.log('用户已登录，开始获取热榜平台数据...');
      refetchPlatforms();
    } else {
      console.log('用户未登录，跳过热榜平台数据获取');
      // 清空数据
      setPlatforms([]);
      setSelectedPlatform(null);
      setNews([]);
    }
  }, [isAuthenticated, refetchPlatforms]);

  // Fetch news when selected platform changes
  useEffect(() => {
    if (selectedPlatform) {
      refetchNews();
    }
  }, [selectedPlatform, refetchNews]);

  return {
    platforms,
    selectedPlatform,
    setSelectedPlatform,
    news,
    isLoadingPlatforms,
    isLoadingNews,
    error,
    refetchPlatforms,
    refetchNews
  };
};
