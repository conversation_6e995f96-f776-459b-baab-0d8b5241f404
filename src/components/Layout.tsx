import React, { useState, useEffect } from 'react';
import Navbar from './Navbar';
import { useLocation, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { useAppContext } from '@/context/AppContext';
import NavigationPanel from './navigation/NavigationPanel';
import FloatingWidgets from './common/FloatingWidgets';
import { getBackendConfig } from '@/config/backend';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isNavOpen, setIsNavOpen] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { language, user, isAuthReady, setUser } = useAppContext();

  // 使用AppContext中的认证状态，避免重复请求
  const isAuthenticated = isAuthReady && user !== null;
  const isAdmin = isAuthenticated && (user?.is_super_admin === true ||
    (user?.roles && Array.isArray(user.roles) && user.roles.includes('admin')));

  // Layout不再需要独立的认证检查，使用AppContext中的状态

  const handleLogout = async () => {
    try {
      // 调用后端登出API
      const config = getBackendConfig();
      const baseURL = config.goBackend?.baseUrl;
      const token = localStorage.getItem('authToken') || localStorage.getItem('auth_token');

      if (token) {
        try {
          await fetch(`${baseURL}/auth/logout`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          console.error('Backend logout failed:', error);
          // 即使后端登出失败，也要清除本地token
        }
      }

      // 清除本地存储的认证信息
      localStorage.removeItem('authToken');
      localStorage.removeItem('auth_token');

      // 使用AppContext的setUser来更新全局状态
      setUser(null);

      // 触发认证状态变化事件
      window.dispatchEvent(new Event('authStateChanged'));

      toast({
        description: language === 'zh' ? "登出成功" : "You have been logged out successfully.",
      });

      // 获取当前路径
      const currentPath = window.location.pathname;

      // 如果当前在首页，刷新页面以确保首页内容更新
      if (currentPath === '/') {
        window.location.reload();
      } else {
        // 如果不在首页，则导航回首页
        navigate('/');
      }
    } catch (error) {
      console.error('Error during logout:', error);
      toast({
        variant: "destructive",
        description: language === 'zh' ? "登出失败，请重试" : "Failed to log out. Please try again.",
      });
    }
  };

  const toggleNavPanel = () => {
    setIsNavOpen(!isNavOpen);
  };

  if (!isAuthReady) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>;
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar
        onNavToggle={toggleNavPanel}
      />
      {isNavOpen && (
        <NavigationPanel
          isAuthenticated={isAuthenticated}
          isAdmin={isAdmin}
          onClose={() => setIsNavOpen(false)}
        />
      )}
      <main className="flex-1">
        {children}
      </main>
      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} g2.al. All rights reserved.
          </p>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <a href="#" className="hover:text-foreground">Terms</a>
            <a href="#" className="hover:text-foreground">Privacy</a>
            <a href="#" className="hover:text-foreground">Contact</a>
          </div>
        </div>
      </footer>

      {/* Use the new floating widgets component, displayed even when not logged in */}
      <FloatingWidgets />
    </div>
  );
};

export default Layout;
