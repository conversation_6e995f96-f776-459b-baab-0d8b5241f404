import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import apiClient from '@/services/api';
import Forbidden from '@/pages/Forbidden';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false
}) => {
  const location = useLocation();
  const { user, isAuthReady, setUser } = useAppContext();

  // 使用AppContext中的认证状态，避免重复请求
  const isAuthenticated = isAuthReady && user !== null;
  const isAdmin = isAuthenticated && (user?.is_super_admin === true ||
    (user?.roles && Array.isArray(user.roles) && user.roles.includes('admin')));

  // 显示加载状态
  if (!isAuthReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // 检查认证状态
  if (!isAuthenticated) {
    return <Forbidden onAuthSuccess={() => window.location.reload()} />;
  }

  // 检查管理员权限（如果需要）
  if (requireAdmin && !isAdmin) {
    return <Forbidden onAuthSuccess={() => window.location.reload()} />;
  }

  // 认证通过，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
