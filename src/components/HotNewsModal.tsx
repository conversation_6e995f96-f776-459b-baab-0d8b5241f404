import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, TrendingUp, X } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';

interface NewsItem {
  title: string;
  url: string;
  hot?: string | number;
  desc?: string;
  pic?: string;
}

interface NewsPlatform {
  path: string;
  name: string;
  chineseName: string;
  sort: number;
}

interface HotNewsModalProps {
  isOpen: boolean;
  onClose: () => void;
  platform: NewsPlatform | null;
}

const HotNewsModal: React.FC<HotNewsModalProps> = ({ isOpen, onClose, platform }) => {
  const { language } = useAppContext();
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取热榜数据
  const fetchNews = async (platformPath: string) => {
    setIsLoading(true);
    setError(null);
    try {
      console.log(`获取${platformPath}平台的热榜数据...`);
      
      // 调用外部API获取热榜数据
      const response = await fetch(`https://api.allbs.cn/wx/hot/getHotNews${platformPath}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && Array.isArray(data)) {
        setNews(data.slice(0, 20)); // 只显示前20条
        console.log(`成功获取${platformPath}平台的热榜数据:`, data.slice(0, 20));
      } else {
        setNews([]);
        console.log(`${platformPath}平台暂无热榜数据`);
      }
    } catch (error) {
      console.error(`获取${platformPath}平台热榜数据失败:`, error);
      setError(language === 'en' ? 'Failed to load hot topics' : '加载热榜数据失败');
      setNews([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 当平台改变时获取数据
  useEffect(() => {
    if (isOpen && platform) {
      fetchNews(platform.path);
    }
  }, [isOpen, platform]);

  // 重试获取数据
  const handleRetry = () => {
    if (platform) {
      fetchNews(platform.path);
    }
  };

  if (!platform) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-accent1-500 flex items-center justify-center mr-3 text-white text-sm font-medium">
                {platform.chineseName?.charAt(0) || platform.name?.charAt(0) || '?'}
              </div>
              <span>
                {language === 'en' ? platform.name : platform.chineseName}
                {language === 'en' ? ' Hot Topics' : ' 热榜'}
              </span>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 10 }).map((_, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <Skeleton className="w-6 h-6 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={handleRetry} variant="outline">
                {language === 'en' ? 'Retry' : '重试'}
              </Button>
            </div>
          ) : news.length === 0 ? (
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {language === 'en' ? 'No hot topics available' : '暂无热榜数据'}
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {news.map((item, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <Badge variant="secondary" className="flex-shrink-0 mt-1">
                    {index + 1}
                  </Badge>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm leading-tight mb-1 line-clamp-2">
                      {item.title}
                    </h3>
                    {item.desc && (
                      <p className="text-xs text-muted-foreground line-clamp-1 mb-2">
                        {item.desc}
                      </p>
                    )}
                    <div className="flex items-center justify-between">
                      {item.hot && (
                        <span className="text-xs text-orange-500 font-medium">
                          🔥 {item.hot}
                        </span>
                      )}
                      {item.url && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => window.open(item.url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          {language === 'en' ? 'View' : '查看'}
                        </Button>
                      )}
                    </div>
                  </div>
                  {item.pic && (
                    <img
                      src={item.pic}
                      alt={item.title}
                      className="w-16 h-12 object-cover rounded flex-shrink-0"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default HotNewsModal;
