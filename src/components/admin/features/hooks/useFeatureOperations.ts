import { getBackendConfig } from '@/config/backend';
import { FeatureConfig } from '../types/feature-types';
import { defaultFeatures } from '../utils/featureConstants';

// 后端返回的数据结构
interface BackendFeatureConfig {
  id: string;
  name: string;
  description: string;
  visible: boolean;
  sort_order: number;
}

export const fetchFeatures = async (): Promise<FeatureConfig[]> => {
  try {
    // Try to get features from Go backend
    const config = getBackendConfig();
    const token = localStorage.getItem('authToken');

    const response = await fetch(`${config.goBackend?.baseUrl}/admin/features/banner`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
    });

    if (response.ok) {
      const data = await response.json();
      if (data && Array.isArray(data)) {
        // 转换后端数据结构为前端期望的格式
        const convertedFeatures: FeatureConfig[] = data.map((backendFeature: BackendFeatureConfig) => {
          // 查找对应的默认配置来获取完整信息
          const defaultFeature = defaultFeatures.find(df => df.id === backendFeature.id);

          return {
            id: backendFeature.id,
            title: defaultFeature?.title || { en: backendFeature.name, zh: backendFeature.name },
            description: defaultFeature?.description || { en: backendFeature.description, zh: backendFeature.description },
            icon: defaultFeature?.icon || 'star',
            iconBgGradient: defaultFeature?.iconBgGradient || 'from-gray-500 to-gray-600',
            badge: defaultFeature?.badge,
            order: backendFeature.sort_order || defaultFeature?.order || 0,
            visible: backendFeature.visible,
            section: defaultFeature?.section || 'main'
          };
        });

        console.log('Converted features from backend:', convertedFeatures);
        return convertedFeatures;
      }
    }

    // If there's an error or no data, return default features
    console.log('Using default features');
    return defaultFeatures;
  } catch (err) {
    console.warn('Failed to fetch features, using defaults:', err);
    return defaultFeatures;
  }
};

export const saveFeatures = async (features: FeatureConfig[]): Promise<void> => {
  try {
    const config = getBackendConfig();
    const token = localStorage.getItem('authToken');
    
    if (!token) {
      throw new Error('您必须登录后才能保存配置');
    }
    
    const response = await fetch(`${config.goBackend?.baseUrl}/admin/features/banner`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(features)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || '保存配置失败');
    }
  } catch (err) {
    console.error('Failed to save features:', err);
    throw err;
  }
};
