import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '@/context/AppContext';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ListTodo, StickyNote, Plus, ArrowRight, Calendar, Clock, Check } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useTodos } from '@/hooks/useTodos';
import { useMemos, type Memo } from '@/hooks/useMemos';
import ReactMarkdown from 'react-markdown';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface TodoMemoPreviewProps {
  userId: string | number;
}

const TodoMemoPreview: React.FC<TodoMemoPreviewProps> = ({ userId }) => {
  const { language } = useAppContext();
  const navigate = useNavigate();
  const [selectedMemo, setSelectedMemo] = useState<Memo | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const { todos, loading: todosLoading } = useTodos(userId);
  const { memos, loading: memosLoading } = useMemos();
  
  const isLoading = todosLoading || memosLoading;
  
  // 获取未完成的待办，按到期日期排序，最多3条
  const activeTodos = todos
    .filter(todo => !todo.completed)
    .sort((a, b) => {
      if (!a.due_date) return 1;
      if (!b.due_date) return -1;
      return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
    })
    .slice(0, 3);
  
  // 获取最新的备忘录，按创建时间倒序排序，最多3条
  const recentMemos = memos
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 3);

  // 处理查看备忘录
  const handleViewMemo = (memo: Memo) => {
    setSelectedMemo(memo);
    setIsDialogOpen(true);
  };

  // 简单的文本格式化，支持基础Markdown语法
  const formatContent = (content: string): string => {
    // 安全检查：确保content不为undefined或null
    if (!content || typeof content !== 'string') {
      return '';
    }

    // 移除可能导致XSS的内容，增加安全性
    let sanitizedContent = content
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    // 确保换行符被保留
    sanitizedContent = sanitizedContent.replace(/\n/g, '  \n');

    return sanitizedContent;
  };

  // 自定义渲染Markdown内容
  const renderMarkdown = (text: string): React.ReactNode => {
    // 安全检查：确保text不为undefined或null
    if (!text || typeof text !== 'string') {
      return <p className="text-sm text-muted-foreground">无内容</p>;
    }

    try {
      return (
        <ReactMarkdown
          components={{
            // 自定义链接渲染
            a: ({ node, ...props }) => (
              <a {...props} target="_blank" rel="noopener noreferrer" className="text-primary underline" />
            ),
            // 自定义代码块渲染
            code: ({ node, ...props }) => (
              <code {...props} className="px-1 py-0.5 bg-muted rounded text-xs" />
            ),
            // 限制图片最大尺寸
            img: ({ node, ...props }) => (
              <img
                {...props}
                alt={props.alt || '图片'}
                className="max-w-full h-auto rounded-md my-2"
                style={{ maxHeight: '120px' }}
              />
            ),
          }}
        >
          {formatContent(text)}
        </ReactMarkdown>
      );
    } catch (err) {
      console.error('Markdown渲染错误:', err);
      return <p className="text-sm text-destructive">内容显示错误</p>;
    }
  };

  // 简化的Markdown渲染组件
  const MarkdownContent = ({ content, maxHeight }: { content: string; maxHeight?: string }) => {
    return (
      <div className={cn("text-sm prose prose-sm dark:prose-invert max-w-none break-words", maxHeight && "overflow-hidden")} style={maxHeight ? { maxHeight } : {}}>
        {renderMarkdown(content)}
      </div>
    );
  };

  // 截断文本显示
  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  
  return (
    <>
      <Card className="border relative">
        <Badge className="absolute top-4 right-4 bg-accent1-500 text-white">
          {language === 'en' ? 'NEW' : '新功能'}
        </Badge>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center">
              <div className="flex items-center gap-1">
                <ListTodo className="mr-1 h-5 w-5 text-primary" />
                <span className="mr-1">/</span>
                <StickyNote className="mr-2 h-5 w-5 text-primary" />
              </div>
              {language === 'en' ? 'Todos & Memos' : '待办与备忘录'}
            </CardTitle>
            <Button asChild variant="outline" size="sm">
              <div onClick={() => navigate('/memo-todo')}>
                {language === 'en' ? 'Manage' : '管理'}
              </div>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pb-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Skeleton key={i} className="h-24 w-full rounded-lg" />
                ))}
              </div>
              <div className="space-y-3">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Skeleton key={i} className="h-24 w-full rounded-lg" />
                ))}
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 待办事项卡片 */}
              <div>
                <div className="flex items-center mb-3">
                  <ListTodo className="h-4 w-4 mr-2 text-primary" />
                  <span className="text-sm font-medium">
                    {language === 'en' ? 'Active Todos' : '进行中的待办'}
                  </span>
                  <Badge variant="secondary" className="ml-2">{todos.filter(t => !t.completed).length}</Badge>
                </div>
                
                {activeTodos.length === 0 ? (
                  <div className="text-center py-8 text-sm text-muted-foreground border border-dashed rounded-lg bg-muted/5 flex flex-col items-center justify-center">
                    <ListTodo className="h-8 w-8 mb-2 text-muted-foreground/50" />
                    {language === 'en' ? 'No active todos' : '没有进行中的待办'}
                    <Button 
                      variant="link" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => navigate('/memo-todo')}
                    >
                      <Plus className="h-3.5 w-3.5 mr-1" />
                      {language === 'en' ? 'Add Todo' : '添加待办'}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {activeTodos.map((todo, index) => (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        key={todo.id} 
                        className="p-3 rounded-lg border border-border/60 hover:border-primary/30 bg-card hover:bg-accent/5 transition-all shadow-sm hover:shadow cursor-pointer"
                        onClick={() => navigate('/memo-todo')}
                      >
                        <div className="flex items-start gap-3">
                          <div className="h-6 w-6 rounded-full border border-primary/30 flex items-center justify-center flex-shrink-0 mt-0.5 hover:bg-primary/10 transition-colors">
                            <Check className="h-3 w-3 text-transparent hover:text-primary/50" />
                          </div>
                          <div className="overflow-hidden flex-1">
                            <p className="text-sm font-medium line-clamp-1">{todo.title}</p>
                            {todo.due_date && (
                              <div className="flex items-center mt-1.5 text-xs text-muted-foreground">
                                <Calendar className="h-3.5 w-3.5 mr-1.5" />
                                {new Date(todo.due_date).toLocaleDateString(
                                  language === 'en' ? 'en-US' : 'zh-CN',
                                  { year: 'numeric', month: 'short', day: 'numeric' }
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* 备忘录卡片 */}
              <div>
                <div className="flex items-center mb-3">
                  <StickyNote className="h-4 w-4 mr-2 text-primary" />
                  <span className="text-sm font-medium">
                    {language === 'en' ? 'Recent Memos' : '最近的备忘录'}
                  </span>
                  <Badge variant="secondary" className="ml-2">{memos.length}</Badge>
                </div>
                
                {recentMemos.length === 0 ? (
                  <div className="text-center py-8 text-sm text-muted-foreground border border-dashed rounded-lg bg-muted/5 flex flex-col items-center justify-center">
                    <StickyNote className="h-8 w-8 mb-2 text-muted-foreground/50" />
                    {language === 'en' ? 'No memos yet' : '暂无备忘录'}
                    <Button 
                      variant="link" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => navigate('/memo-todo')}
                    >
                      <Plus className="h-3.5 w-3.5 mr-1" />
                      {language === 'en' ? 'Add Memo' : '添加备忘录'}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentMemos.map((memo, index) => (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 + index * 0.1 }}
                        key={memo.id} 
                        className="p-3 rounded-lg border border-border/60 hover:border-primary/30 bg-card hover:bg-accent/5 transition-all shadow-sm hover:shadow"
                      >
                        <div className="max-h-[80px] overflow-hidden">
                          <MarkdownContent content={memo.content} maxHeight="80px" />
                        </div>
                        <div className="flex items-center justify-between mt-2 pt-1 border-t border-border/40">
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Clock className="h-3.5 w-3.5 mr-1.5" />
                            {new Date(memo.created_at).toLocaleDateString(
                              language === 'en' ? 'en-US' : 'zh-CN',
                              { year: 'numeric', month: 'short', day: 'numeric' }
                            )}
                          </div>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-6 px-2 text-xs"
                            onClick={() => handleViewMemo(memo)}
                          >
                            {language === 'en' ? 'View' : '查看'}
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="pt-2 border-t">
          <Button 
            onClick={() => navigate('/memo-todo')}
            className="mx-auto group"
            variant="outline"
            size="sm"
          >
            {language === 'en' ? 'View All Todos & Memos' : '查看全部待办与备忘录'}
            <ArrowRight className="ml-1.5 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </CardFooter>
      </Card>

      {/* 备忘录详情对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <StickyNote className="h-5 w-5 text-primary" />
              {language === 'en' ? 'Memo Details' : '备忘录详情'}
            </DialogTitle>
          </DialogHeader>
          {selectedMemo && (
            <div className="space-y-4">
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="h-4 w-4 mr-2" />
                {language === 'en' ? 'Created' : '创建时间'}：
                {new Date(selectedMemo.created_at).toLocaleString(
                  language === 'en' ? 'en-US' : 'zh-CN',
                  { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }
                )}
              </div>
              <div className="prose prose-sm dark:prose-invert max-w-none">
                <MarkdownContent content={selectedMemo.content} />
              </div>
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  {language === 'en' ? 'Close' : '关闭'}
                </Button>
                <Button onClick={() => navigate('/memo-todo')}>
                  {language === 'en' ? 'Edit in Manager' : '在管理器中编辑'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TodoMemoPreview;
