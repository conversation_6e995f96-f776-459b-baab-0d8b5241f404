import React, { useState, useEffect, useRef, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useAppContext } from "@/context/AppContext";
import { useMemos, Memo } from "@/hooks/useMemos";
import { useTodos, Todo } from "@/hooks/useTodos";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import { format, isBefore, isToday, isTomorrow, isAfter, parseISO, isValid } from "date-fns";
import { zhCN, enUS } from "date-fns/locale";
import { cn } from "@/lib/utils";
import HeaderSection from "./memo-todo/HeaderSection";
import SearchBar from "./memo-todo/SearchBar";
import SidebarNav from "./memo-todo/SidebarNav";
import TodoSection from "./memo-todo/TodoSection";
import MemoSection from "./memo-todo/MemoSection";
import MemoDialog from "./memo-todo/MemoDialog";
import MemoDetailDialog from "./memo-todo/MemoDetailDialog";
import { Edit2, Trash2 } from 'lucide-react'; // Import icons for detail view
import { MarkdownMemo } from '@/components/MarkdownMemo'; // Ensure MarkdownMemo is imported for memo detail view
import { Badge } from '@/components/ui/badge'; // Ensure Badge is imported for detail view
import TodoEditDialog from "./memo-todo/TodoEditDialog";
import PageWrapper from '@/components/layout/PageWrapper';
import { AuthModal } from '@/components/auth/AuthModal';

const MemoTodo = () => {
  const { language, user, isAuthReady } = useAppContext();
  const navigate = useNavigate();
  const { toast } = useToast();

  // 数据状态与钩子
  const { memos, loading: memosLoading, addMemo, updateMemo, deleteMemo, refreshMemos } = useMemos();
  const { todos, loading: todosLoading, addTodo, toggleTodo, updateTodo, deleteTodo, refreshTodos } = useTodos();

  // UI状态
  const [newMemo, setNewMemo] = useState('');
  const [newTodo, setNewTodo] = useState('');
  const [searchText, setSearchText] = useState('');
  const [memoDialogOpen, setMemoDialogOpen] = useState(false);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [currentMemo, setCurrentMemo] = useState<Memo | null>(null);
  const [completedVisibility, setCompletedVisibility] = useState('active'); // 默认筛选进行中
  const [activeCategory, setActiveCategory] = useState('all');
  const [dueDateFilter, setDueDateFilter] = useState<string>('upcoming'); // 默认筛选即将到来
  const memoInputRef = useRef<HTMLTextAreaElement>(null);
  const todoInputRef = useRef<HTMLInputElement>(null);

  const [selectedDueDate, setSelectedDueDate] = useState<Date | undefined>(undefined);
  const [selectedReminderDate, setSelectedReminderDate] = useState<Date | undefined>(undefined);
  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high' | undefined>(undefined);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  const [isEditMode, setIsEditMode] = useState(false); // This might be reused or adapted for the detail view's edit functionality

  const [todoEditDialogOpen, setTodoEditDialogOpen] = useState(false);
  const [currentTodo, setCurrentTodo] = useState<Todo | null>(null);
  const [currentView, setCurrentView] = useState<'list' | 'memoDetail' | 'todoDetail'>('list'); // 'list', 'memoDetail', 'todoDetail'
  const [selectedItem, setSelectedItem] = useState<Memo | Todo | null>(null);

  // Pagination state for memos
  const [currentMemoPage, setCurrentMemoPage] = useState(1);
  const memosPerPage = 6; // Display 6 memos per page, adjust as needed

  // 认证弹框状态
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  // 认证弹框处理函数
  const handleLogin = () => {
    setAuthMode('login');
    setIsAuthModalOpen(true);
  };

  const handleAuthModalClose = () => {
    setIsAuthModalOpen(false);
  };

  // 当认证状态改变时刷新数据
  useEffect(() => {
    // 只在用户登录时获取数据
    if (user) {
      refreshMemos();
      refreshTodos();
    }
  }, [user, refreshMemos, refreshTodos]);

  // 筛选待办事项 - 使用useMemo减少不必要的重新计算
  const filteredTodos = useMemo(() => {
    return todos.filter(todo => {
      // 文本搜索筛选
      const matchesSearch = todo.title.toLowerCase().includes(searchText.toLowerCase());

      // 完成状态筛选
      const matchesCompletion =
        completedVisibility === 'all' ||
        (completedVisibility === 'active' && !todo.completed) ||
        (completedVisibility === 'completed' && todo.completed);

      // 日期筛选
      let matchesDueDate = true;

      try {
        if (dueDateFilter !== 'all') {
          if (dueDateFilter === 'no-date') {
            matchesDueDate = !todo.due_date;
          } else if (todo.due_date) {
            const dueDate = parseISO(todo.due_date);
            if (isValid(dueDate)) {
              if (dueDateFilter === 'today') {
                matchesDueDate = isToday(dueDate);
              } else if (dueDateFilter === 'tomorrow') {
                matchesDueDate = isTomorrow(dueDate);
              } else if (dueDateFilter === 'upcoming') {
                matchesDueDate = isAfter(dueDate, new Date()) && !isToday(dueDate) && !isTomorrow(dueDate);
              } else if (dueDateFilter === 'overdue') {
                matchesDueDate = isBefore(dueDate, new Date()) && !isToday(dueDate) && !todo.completed;
              } else {
                matchesDueDate = true;
              }
            } else {
              matchesDueDate = dueDateFilter === 'no-date';
            }
          } else {
            matchesDueDate = dueDateFilter === 'no-date';
          }
        }
      } catch (error) {
        // 日期解析错误，默认显示
        matchesDueDate = true;
      }

      return matchesSearch && matchesCompletion && matchesDueDate;
    });
  }, [todos, searchText, completedVisibility, dueDateFilter]);

  // 筛选备忘录 - 使用useMemo减少不必要的重新计算
  const filteredMemos = useMemo(() => {
    return memos.filter(memo => {
      // 安全检查memo.content是否存在
      if (!memo || !memo.content || typeof memo.content !== 'string') {
        return false;
      }

      // 文本搜索筛选
      const matchesSearch = memo.content.toLowerCase().includes(searchText.toLowerCase());

      // 标签筛选 (如果activeCategory是标签)
      let matchesCategory = true;

      if (activeCategory !== 'all') {
        if (!memo.tags || memo.tags.length === 0) {
          matchesCategory = false;
        } else {
          matchesCategory = memo.tags.some(
            tag => tag && typeof tag === 'string' && tag.toLowerCase() === activeCategory.toLowerCase()
          );
        }
      }

      return matchesSearch && matchesCategory;
    });
  }, [memos, searchText, activeCategory]);

  // 获取所有可用的标签类别
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    memos.forEach(memo => {
      if (memo.tags && Array.isArray(memo.tags) && memo.tags.length > 0) {
        memo.tags.forEach(tag => {
          if (tag && typeof tag === 'string' && tag.trim()) {
            tagSet.add(tag.toLowerCase());
          }
        });
      }
    });
    return Array.from(tagSet);
  }, [memos]);

  const handleAddMemo = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMemo.trim()) {
      if (currentMemo && isEditMode) {
        updateMemo(currentMemo.id, {
          content: newMemo.trim(),
          reminder_date: selectedReminderDate ? selectedReminderDate.toISOString() : null,
          tags: selectedTags.length > 0 ? selectedTags : null
        });
      } else {
        addMemo(
          newMemo.trim(),
          selectedReminderDate ? selectedReminderDate.toISOString() : undefined,
          selectedTags.length > 0 ? selectedTags : undefined
        );
      }
      resetMemoForm();
    }
  };

  const resetMemoForm = () => {
    setNewMemo('');
    setSelectedReminderDate(undefined);
    setSelectedTags([]);
    setMemoDialogOpen(false);
    setIsEditMode(false);
    setCurrentMemo(null);
  };

  const handleAddTodo = (e: React.FormEvent) => {
    e.preventDefault();
    if (newTodo.trim()) {
      addTodo(
        newTodo.trim(),
        selectedDueDate ? selectedDueDate.toISOString() : undefined,
        selectedPriority
      );
      setNewTodo('');
      setSelectedDueDate(undefined);
      setSelectedPriority(undefined);
      todoInputRef.current?.focus();
    }
  };

  const handleEditTodo = () => {
    if (currentTodo && newTodo.trim()) {
      updateTodo(currentTodo.id, {
        title: newTodo.trim(),
        due_date: selectedDueDate ? selectedDueDate.toISOString() : null,
        priority: selectedPriority
      });
      setNewTodo('');
      setSelectedDueDate(undefined);
      setSelectedPriority(undefined);
      setTodoEditDialogOpen(false);
      setCurrentTodo(null);
    }
  };

  const handleOpenTodoEdit = (todo: Todo) => {
    setCurrentTodo(todo);
    setNewTodo(todo.title);
    setSelectedDueDate(todo.due_date ? parseISO(todo.due_date) : undefined);
    setSelectedPriority(todo.priority || undefined);
    setTodoEditDialogOpen(true);
  };

  const handleViewMemoDetails = (memo: Memo) => {
    setCurrentMemo(memo);
    setDetailDialogOpen(true);
  };

  const handleShowMemoDetails = (memo: Memo) => {
    setSelectedItem(memo);
    setCurrentView('memoDetail');
  };

  const handleShowTodoDetails = (todo: Todo) => {
    setSelectedItem(todo);
    setCurrentView('todoDetail');
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedItem(null);
  };

  const handleEditMemo = (memo: Memo) => {
    setCurrentMemo(memo);
    setNewMemo(memo.content);
    setSelectedReminderDate(memo.reminder_date ? parseISO(memo.reminder_date) : undefined);
    // 安全处理标签数组
    const safeTags = memo.tags && Array.isArray(memo.tags) 
      ? memo.tags.filter(tag => tag && typeof tag === 'string').map(tag => tag.toString())
      : [];
    setSelectedTags(safeTags);
    setIsEditMode(true);
    setMemoDialogOpen(true);
  };

  const addTag = () => {
    if (newTag.trim() && !selectedTags.includes(newTag.trim())) {
      setSelectedTags([...selectedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setSelectedTags(selectedTags.filter(tag => tag !== tagToRemove));
  };

  // 显示未认证的用户登录提示
  if (!isAuthReady) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-4rem)]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-4rem)]">
        <Card className="w-full max-w-md p-6">
          <CardContent className="text-center space-y-4">
            <div className="text-4xl text-muted-foreground">🔒</div>
            <p className="text-lg font-medium">
              {language === 'zh' ? '请登录以访问此页面' : 'Please log in to access this page'}
            </p>
            <Button onClick={handleLogin}>
              {language === 'zh' ? '登录' : 'Log In'}
            </Button>
          </CardContent>
        </Card>

        {/* 认证弹框 */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={handleAuthModalClose}
          defaultMode={authMode}
        />
      </div>
    );
  }

  if (memosLoading || todosLoading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-4rem)] flex-col gap-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <p className="text-muted-foreground">
          {language === 'zh' ? '加载中...' : 'Loading...'}
        </p>
      </div>
    );
  }

  const headerContent = <HeaderSection language={language} />;

  return (
    <PageWrapper headerContent={headerContent}>
      <div className="max-w-7xl mx-auto px-4 pt-1 md:pt-2 space-y-4">

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-20">
            <SearchBar
              language={language}
              searchText={searchText}
              setSearchText={setSearchText}
            />
            <div className="mt-4">
              <SidebarNav
                language={language}
                todos={todos}
                filteredTodos={filteredTodos}
                activeCategory={activeCategory}
                setActiveCategory={setActiveCategory}
                completedVisibility={completedVisibility}
                setCompletedVisibility={setCompletedVisibility}
                dueDateFilter={dueDateFilter}
                setDueDateFilter={setDueDateFilter}
                availableTags={allTags}
              />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-4">
          {currentView === 'list' ? (
            <>
              <TodoSection
                language={language}
                newTodo={newTodo}
                setNewTodo={setNewTodo}
                todoInputRef={todoInputRef}
                selectedDueDate={selectedDueDate}
                setSelectedDueDate={setSelectedDueDate}
                selectedPriority={selectedPriority}
                setSelectedPriority={setSelectedPriority}
                handleAddTodo={handleAddTodo}
                todos={filteredTodos}
                toggleTodo={(id: string) => toggleTodo(id, !todos.find(t => t.id === id)?.completed)}
                handleOpenTodoEdit={handleOpenTodoEdit}
                deleteTodo={deleteTodo}
                handleViewTodoDetails={handleShowTodoDetails}
                todosLoading={todosLoading}
              />

              <MemoSection
                language={language}
                searchText={searchText}
                handleViewMemoDetails={handleShowMemoDetails} // Use the new handler for detail view
                handleEditMemo={handleEditMemo} // Keep for edit dialog functionality
                deleteMemo={deleteMemo}
                memos={filteredMemos}
                setMemoDialogOpen={setMemoDialogOpen}
                setIsEditMode={setIsEditMode}
                setNewMemo={setNewMemo}
                setSelectedReminderDate={setSelectedReminderDate}
                setSelectedTags={setSelectedTags}
                currentPage={currentMemoPage} // Pass current page
                itemsPerPage={memosPerPage} // Pass items per page
                onPageChange={setCurrentMemoPage} // Pass page change handler
              />
            </>
          ) : currentView === 'memoDetail' && selectedItem && 'content' in selectedItem ? (
            <Card>
              <CardContent className="p-4">
                <Button onClick={handleBackToList} variant="outline" className="mb-4">
                  {language === 'zh' ? '返回列表' : 'Back to List'}
                </Button>
                <h2 className="text-2xl font-semibold mb-3">{(selectedItem as Memo).content.substring(0, 50) + ((selectedItem as Memo).content.length > 50 ? '...' : '')}</h2>
                <div className="prose prose-sm max-w-none dark:prose-invert mb-4">
                  <MarkdownMemo content={(selectedItem as Memo).content} />
                </div>
                {(selectedItem as Memo).tags && Array.isArray((selectedItem as Memo).tags) && ((selectedItem as Memo).tags?.length ?? 0) > 0 && (
                  <div className="mb-2">
                    <strong>{language === 'zh' ? '标签:' : 'Tags:'}</strong>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {(selectedItem as Memo).tags!
                        .filter(tag => tag && typeof tag === 'string')
                        .map((tag, index) => (
                          <Badge key={index} variant="secondary">{tag}</Badge>
                        ))}
                    </div>
                  </div>
                )}
                {(selectedItem as Memo).reminder_date && (
                  <p className="text-sm text-muted-foreground mb-1">
                    <strong>{language === 'zh' ? '提醒日期:' : 'Reminder:'}</strong> {format(parseISO((selectedItem as Memo).reminder_date!), 'PPP p', { locale: language === 'zh' ? zhCN : enUS })}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mb-4">
                  <strong>{language === 'zh' ? '创建于:' : 'Created:'}</strong> {format(parseISO((selectedItem as Memo).created_at), 'PPP p', { locale: language === 'zh' ? zhCN : enUS })}
                </p>
                <div className="flex gap-2">
                  <Button onClick={() => handleEditMemo(selectedItem as Memo)} size="sm">
                    <Edit2 className="h-4 w-4 mr-2" />
                    {language === 'zh' ? '编辑' : 'Edit'}
                  </Button>
                  <Button onClick={() => { deleteMemo((selectedItem as Memo).id); handleBackToList(); }} variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    {language === 'zh' ? '删除' : 'Delete'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : currentView === 'todoDetail' && selectedItem && 'title' in selectedItem ? (
            <Card>
              <CardContent className="p-4">
                <Button onClick={handleBackToList} variant="outline" className="mb-4">
                  {language === 'zh' ? '返回列表' : 'Back to List'}
                </Button>
                <h2 className="text-2xl font-semibold mb-3">{(selectedItem as Todo).title}</h2>
                <p className={cn("mb-2", (selectedItem as Todo).completed ? "text-green-600" : "text-amber-600")}>
                  <strong>{language === 'zh' ? '状态: ' : 'Status: '}</strong>
                  {(selectedItem as Todo).completed ? (language === 'zh' ? '已完成' : 'Completed') : (language === 'zh' ? '进行中' : 'Pending')}
                </p>
                {(selectedItem as Todo).due_date && (
                  <p className="text-sm text-muted-foreground mb-1">
                    <strong>{language === 'zh' ? '截止日期:' : 'Due Date:'}</strong> {format(parseISO((selectedItem as Todo).due_date!), 'PPP', { locale: language === 'zh' ? zhCN : enUS })}
                  </p>
                )}
                {(selectedItem as Todo).priority && (
                  <p className="text-sm text-muted-foreground mb-1">
                    <strong>{language === 'zh' ? '优先级:' : 'Priority:'}</strong>
                    <Badge variant={(selectedItem as Todo).priority === 'high' ? 'destructive' : (selectedItem as Todo).priority === 'medium' ? 'secondary' : 'outline'} className="ml-1">
                      {language === 'zh'
                        ? (selectedItem as Todo).priority === 'high'
                          ? '高'
                          : (selectedItem as Todo).priority === 'medium'
                            ? '中'
                            : '低'
                        : (selectedItem as Todo).priority === 'high'
                          ? 'High'
                          : (selectedItem as Todo).priority === 'medium'
                            ? 'Medium'
                            : 'Low'}
                    </Badge>
                  </p>
                )}
                 <p className="text-sm text-muted-foreground mb-4">
                  <strong>{language === 'zh' ? '创建于:' : 'Created:'}</strong> {format(parseISO((selectedItem as Todo).created_at), 'PPP p', { locale: language === 'zh' ? zhCN : enUS })}
                </p>
                <div className="flex gap-2">
                  <Button onClick={() => handleOpenTodoEdit(selectedItem as Todo)} size="sm">
                    <Edit2 className="h-4 w-4 mr-2" />
                    {language === 'zh' ? '编辑' : 'Edit'}
                  </Button>
                  <Button onClick={() => { deleteTodo((selectedItem as Todo).id); handleBackToList(); }} variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    {language === 'zh' ? '删除' : 'Delete'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : null}
        </div>
      </div>

      <MemoDialog
        language={language}
        memoDialogOpen={memoDialogOpen}
        setMemoDialogOpen={setMemoDialogOpen}
        isEditMode={isEditMode}
        newMemo={newMemo}
        setNewMemo={setNewMemo}
        selectedReminderDate={selectedReminderDate}
        setSelectedReminderDate={setSelectedReminderDate}
        selectedTags={selectedTags}
        setSelectedTags={setSelectedTags}
        newTag={newTag}
        setNewTag={setNewTag}
        handleAddMemo={handleAddMemo}
        addTag={addTag}
        removeTag={removeTag}
      />
      <MemoDetailDialog
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        memo={currentMemo}
        deleteMemo={deleteMemo}
        handleEditMemo={handleEditMemo}
      />
      <TodoEditDialog
        language={language}
        open={todoEditDialogOpen}
        onOpenChange={setTodoEditDialogOpen}
        newTodo={newTodo}
        setNewTodo={setNewTodo}
        selectedDueDate={selectedDueDate}
        setSelectedDueDate={setSelectedDueDate}
        selectedPriority={selectedPriority}
        setSelectedPriority={setSelectedPriority}
        handleEditTodo={handleEditTodo}
      />
    </div>
    </PageWrapper>
  );
};

export default MemoTodo;
