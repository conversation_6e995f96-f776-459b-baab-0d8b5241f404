import { useState, useEffect, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAppContext } from "@/context/AppContext";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Compass, TrendingUp, Link2, Mail, ListTodo, StickyNote } from "lucide-react";
import TempMailbox from "@/pages/index/TempMailbox";
import TodoMemoSection from "@/components/index/TodoMemoSection";
import AnimatedGradientTitle from '@/components/common/AnimatedGradientTitle';
import UrlShortenerWithDomains from '@/components/url-shortener/UrlShortenerWithDomains';
import AboutSection from "./index/AboutSection";
import CtaSection from "./index/CtaSection";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { fetchPlatforms } from '@/hooks/hot-news/api';
import { NewsPlatform } from '@/hooks/hot-news/types';
import { NavCategory, NavLink, getUserNavigationData } from "@/services/navigationService";
import { getApprovedDomains } from "@/services/domainWhitelistService";
import TodoMemoPreview from "@/components/index/TodoMemoPreview";
import { fetchFeatures } from '@/components/admin/features/hooks/useFeatureOperations';
import { FeatureConfig } from '@/components/admin/features/types/feature-types';
import { renderIcon } from '@/components/admin/features/utils/iconUtils';
import { useBannerConfig } from '@/hooks/useBannerConfig';

const Index = () => {
  const { language, user, isAuthReady, fetchUser, t } = useAppContext();
  const [approvedDomains, setApprovedDomains] = useState<string[]>([]);

  // Banner and features state
  const [bannerFeatures, setBannerFeatures] = useState<FeatureConfig[]>([]);
  const [loadingFeatures, setLoadingFeatures] = useState(true);
  const { bannerConfig } = useBannerConfig();

  // 用户导航数据状态
  const [userCategories, setUserCategories] = useState<NavCategory[]>([]);
  const [userLinks, setUserLinks] = useState<NavLink[]>([]);
  const [isLoadingNavigation, setIsLoadingNavigation] = useState(false);

  // 热榜数据状态
  const [platforms, setPlatforms] = useState<NewsPlatform[]>([]);
  const [isLoadingHotNews, setIsLoadingHotNews] = useState(false);

  // 计算认证状态 - 确保逻辑正确
  const isAuthenticated = isAuthReady && user !== null;

  // 监听认证状态变化
  useEffect(() => {
    // 当用户登录时获取导航数据 - 确保user存在且有id
    if (isAuthenticated && user && user.id) {
      fetchUserNavigation(user.id);
    }

    // 当用户登出时清除导航数据
    if (isAuthReady && !user) {
      setUserCategories([]);
      setUserLinks([]);
      setPlatforms([]); // 清除热榜数据
    }
  }, [isAuthenticated, user]);

  // Fetch banner features
  useEffect(() => {
    const fetchBannerFeatures = async () => {
      try {
        setLoadingFeatures(true);
        const features = await fetchFeatures();
        setBannerFeatures(features.filter(f => f.visible).sort((a, b) => a.order - b.order));
      } catch (error) {
        console.error('Error fetching banner features:', error);
        // Use fallback features if fetch fails
        setBannerFeatures([]);
      } finally {
        setLoadingFeatures(false);
      }
    };
    
    fetchBannerFeatures();
  }, []);

  // 获取批准的域名 - 总是执行，不依赖认证状态
  useEffect(() => {
    const fetchApprovedDomainsData = async () => {
      try {
        const domains = await getApprovedDomains();
        setApprovedDomains(domains);
      } catch (error) {
        console.error('Error fetching approved domains:', error);
        setApprovedDomains([]);
      }
    };

    fetchApprovedDomainsData();
  }, []); // 只在组件挂载时执行一次

  // 获取热榜数据 - 只有在用户登录时才执行
  useEffect(() => {
    const fetchHotNewsData = async () => {
      // 未登录用户不获取热榜数据
      if (!isAuthenticated) {
        setIsLoadingHotNews(false);
        setPlatforms([]); // 清空平台数据
        return;
      }

      setIsLoadingHotNews(true);
      try {
        console.log('用户已登录，开始获取热榜平台数据...');
        const allPlatforms = await fetchPlatforms();
        if (allPlatforms && allPlatforms.length > 0) {
          setPlatforms(allPlatforms.slice(0, 6)); // 只显示前6个平台
          console.log('热榜平台数据获取成功:', allPlatforms.slice(0, 6));
        }
      } catch (error) {
        console.error('获取热榜平台列表失败:', error);
        // 登录用户获取失败时使用默认平台列表作为后备
        const defaultPlatforms = [
          { path: '/zhihu', name: 'zhihu', chineseName: '知乎热榜', sort: 1 },
          { path: '/weibo', name: 'weibo', chineseName: '微博热搜', sort: 2 },
          { path: '/baidu', name: 'baidu', chineseName: '百度热点', sort: 3 },
          { path: '/douyin', name: 'douyin', chineseName: '抖音热点', sort: 4 },
          { path: '/bilibili', name: 'bilibili', chineseName: 'B站热榜', sort: 5 },
          { path: '/netease-news', name: 'netease', chineseName: '网易新闻', sort: 6 }
        ];
        setPlatforms(defaultPlatforms);
      } finally {
        setIsLoadingHotNews(false);
      }
    };

    // 只有当认证状态确定时才决定是否获取热榜数据
    if (isAuthReady) {
      fetchHotNewsData();
    }
  }, [isAuthReady, isAuthenticated]); // 依赖认证状态

  // 获取用户个人导航数据
  const fetchUserNavigation = async (userId: string | number) => {
    setIsLoadingNavigation(true);
    try {
      const navData = await getUserNavigationData();
      setUserCategories(navData.categories);
      setUserLinks(navData.links);
    } catch (error) {
      console.error('获取用户导航数据失败:', error);
      setUserCategories([]);
      setUserLinks([]);
    } finally {
      setIsLoadingNavigation(false);
    }
  };

  // Convert FeatureConfig to FeatureCard format for AnimatedGradientTitle
  const convertToFeatureCards = (features: FeatureConfig[]) => {
    if (!features || !Array.isArray(features)) {
      return [];
    }
    
    return features.map(feature => {
      // 安全检查所有必需的属性
      if (!feature || !feature.title || !feature.description) {
        return {
          id: feature?.id || 'unknown',
          icon: null,
          title: 'Unknown',
          subtitle: 'Unknown',
          gradient: 'from-gray-500 to-gray-600',
          badge: undefined
        };
      }
      
      return {
        id: feature.id,
        icon: renderIcon(feature.icon, 32),
        title: feature.title[language === 'en' ? 'en' : 'zh'] || feature.title['zh'] || 'Unknown',
        subtitle: feature.description[language === 'en' ? 'en' : 'zh'] || feature.description['zh'] || 'Unknown',
        gradient: feature.iconBgGradient || 'from-gray-500 to-gray-600',
        badge: feature.badge ? (feature.badge[language === 'en' ? 'en' : 'zh'] || feature.badge['zh']) : undefined
      };
    });
  };

  return (
    <div className="container px-4 py-12 md:py-16">
      <div className="mx-auto max-w-5xl text-center">
        <AnimatedGradientTitle 
          title={{
            main: language === 'en' ? 'All-in-One Productivity Platform' : '一站式生产力平台',
            subtitle: language === 'en' 
              ? 'Streamline your workflow with powerful tools designed for modern productivity'
              : '使用为现代生产力设计的强大工具简化您的工作流程'
          }}
          features={convertToFeatureCards(bannerFeatures)}
          bannerConfig={bannerConfig}
        />
        <p className="mt-6 text-lg text-muted-foreground">
          {language === 'en' 
            ? 'Your all-in-one productivity hub: shorten URLs, manage temporary emails, explore curated resources, and stay updated with trending topics.'
            : '您的一站式生产力中心：缩短网址、管理临时邮箱、探索精选资源、了解热门话题。'}
        </p>
        
        <div className="mt-8 flex flex-col gap-8">
          {/* Render components based on banner features configuration */}
          {bannerFeatures.map((feature, index) => {
            // Only show visible features that correspond to actual components
            if (!feature.visible) return null;
            
            switch (feature.id) {
              case 'url-shortener':
                return (
                  <div key={feature.id}>
                    <UrlShortenerWithDomains approvedDomains={approvedDomains} />
                  </div>
                );
              
              case 'email':
                return (
                  <div key={feature.id}>
                    <TempMailbox />
                  </div>
                );
              
              case 'navigation':
                return (
                  <div key={feature.id}>
                    {/* 导航特性描述 - 根据用户登录状态显示不同内容 */}
                    {isAuthenticated ? (
                      <Card className="border">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-xl flex items-center">
                              <Compass className="mr-2 h-5 w-5 text-primary" />
                              {language === 'en' ? 'My Navigation' : '我的导航'}
                            </CardTitle>
                            <Button asChild variant="outline" size="sm">
                              <Link to="/dashboard">
                                {language === 'en' ? 'Manage' : '管理'}
                              </Link>
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-0">
                          {isLoadingNavigation ? (
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                              {Array.from({ length: 8 }).map((_, index) => (
                                <div key={index} className="flex flex-col items-center">
                                  <Skeleton className="h-12 w-12 rounded-full" />
                                  <Skeleton className="h-3 w-20 mt-2" />
                                </div>
                              ))}
                            </div>
                          ) : userLinks.length === 0 ? (
                            <div className="text-center py-6">
                              <p className="text-muted-foreground">
                                {language === 'en' 
                                  ? 'No navigation links found. Add some in your dashboard!' 
                                  : '没有发现导航链接。在控制面板中添加一些！'}
                              </p>
                              <Button asChild variant="outline" className="mt-4">
                                <Link to="/dashboard">
                                  {language === 'en' ? 'Add Links' : '添加链接'}
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                              </Button>
                            </div>
                          ) : (
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                              {userLinks.slice(0, 10).map((link) => (
                                <a 
                                  key={link.id}
                                  href={link.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex flex-col items-center p-3 rounded-lg hover:bg-muted/50 transition-colors"
                                >
                                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                                    {link.icon ? (
                                      <img 
                                        src={link.icon} 
                                        alt={link.title} 
                                        className="w-6 h-6"
                                        onError={(e) => {
                                          e.currentTarget.src = 'https://via.placeholder.com/24';
                                        }}
                                      />
                                    ) : (
                                      <Compass className="h-6 w-6 text-primary" />
                                    )}
                                  </div>
                                  <span className="text-sm font-medium text-center line-clamp-1">{link.title}</span>
                                </a>
                              ))}
                            </div>
                          )}
                        </CardContent>
                        {userLinks.length > 0 && (
                          <CardFooter className="pt-2 pb-4 flex justify-center">
                            <Button asChild variant="link" size="sm">
                              <Link to="/navigation">
                                {language === 'en' ? 'View All Navigation' : '查看全部导航'}
                                <ArrowRight className="ml-1 h-4 w-4" />
                              </Link>
                            </Button>
                          </CardFooter>
                        )}
                      </Card>
                    ) : (
                      <div className="bg-muted/30 p-6 rounded-lg border text-center">
                        <div className="flex justify-center mb-4">
                          <div className="p-3 bg-primary/10 rounded-full">
                            <Compass className="h-8 w-8 text-primary" />
                          </div>
                        </div>
                        <h2 className="text-2xl font-bold mb-4">
                          {language === 'en' ? 'Explore Our Navigation Directory' : '探索我们的导航目录'}
                        </h2>
                        <p className="text-muted-foreground mb-4 max-w-3xl mx-auto">
                          {language === 'en' 
                            ? 'Discover a curated collection of useful links and resources. Our navigation directory helps you find valuable websites, tools, and resources across various categories to boost your productivity and learning.'
                            : '发现精心策划的有用链接和资源集合。我们的导航目录帮助您在不同类别中找到有价值的网站、工具和资源，提升您的生产力和学习效率。'}
                        </p>
                        <Button asChild variant="outline" className="mt-4">
                          <Link to="/navigation" className="group">
                            {language === 'en' ? 'View Navigation Directory' : '查看导航目录'}
                            <Compass className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                );
              
              case 'hot-news':
                return (
                  <div key={feature.id}>
                    {/* 热榜特性描述 - 根据用户登录状态显示不同内容 */}
                    {isAuthenticated ? (
                      <Card className="border relative">
                        <Badge className="absolute top-4 right-4 bg-accent1-500 text-white">
                          {language === 'en' ? 'HOT' : '热门'}
                        </Badge>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-xl flex items-center">
                            <TrendingUp className="mr-2 h-5 w-5 text-primary" />
                            {language === 'en' ? 'Today\'s Hot Topics' : '今日热榜'}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pb-4">
                          {isLoadingHotNews ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                              {Array.from({ length: 6 }).map((_, index) => (
                                <div key={index} className="flex items-center p-3 border rounded-lg">
                                  <Skeleton className="w-8 h-8 rounded-full mr-3" />
                                  <Skeleton className="h-4 w-20" />
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                              {platforms.map((platform) => (
                                <Link
                                  key={platform.path}
                                  to={`/hot-news${platform.path}`}
                                  className="flex items-center p-3 border rounded-lg hover:bg-muted/50 transition-colors group"
                                >
                                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-accent1-500 flex items-center justify-center mr-3 text-white text-sm font-medium">
                                    {platform.chineseName.charAt(0)}
                                  </div>
                                  <span className="font-medium group-hover:text-primary transition-colors">
                                    {language === 'en' ? platform.name : platform.chineseName}
                                  </span>
                                </Link>
                              ))}
                            </div>
                          )}
                        </CardContent>
                        <CardFooter className="pt-2 pb-4 flex justify-center">
                          <Button asChild variant="link" size="sm">
                            <Link to="/hot-news">
                              {language === 'en' ? 'View All Hot Topics' : '查看全部热榜'}
                              <ArrowRight className="ml-1 h-4 w-4" />
                            </Link>
                          </Button>
                        </CardFooter>
                      </Card>
                    ) : (
                      <div className="bg-muted/30 p-6 rounded-lg border text-center relative">
                        <Badge className="absolute top-4 right-4 bg-accent1-500 text-white">
                          {language === 'en' ? 'NEW' : '新功能'}
                        </Badge>
                        <div className="flex justify-center mb-4">
                          <div className="p-3 bg-primary/10 rounded-full">
                            <TrendingUp className="h-8 w-8 text-primary" />
                          </div>
                        </div>
                        <h2 className="text-2xl font-bold mb-4">
                          {language === 'en' ? "Today's Hot Lists" : '今日热榜'}
                        </h2>
                        <p className="text-muted-foreground mb-4 max-w-3xl mx-auto">
                          {language === 'en' 
                            ? 'Stay updated with trending topics from various platforms all in one place. Our hot lists aggregate popular content from major websites like Weibo, Zhihu, Douyin, Baidu, and Bilibili, saving you time and keeping you informed.'
                            : '在一处了解各大平台的热门话题。我们的热榜聚合了微博、知乎、抖音、百度和B站等主要网站的热门内容，为您节省时间，让您随时了解最新热点。'}
                        </p>
                        <Button asChild variant="outline" className="mt-4">
                          <Link to="/hot-news" className="group">
                            {language === 'en' ? 'View Hot Lists' : '查看热榜'}
                            <TrendingUp className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                          </Link>
                        </Button>
                      </div>
                    )}
                  </div>
                );
              
              case 'todo-memo':
                return (
                  <div key={feature.id}>
                    {/* 待办和备忘录 - 根据用户登录状态显示不同内容 */}
                    {isAuthenticated && user ? (
                      <TodoMemoPreview userId={user.id} />
                    ) : (
                      <TodoMemoSection />
                    )}
                  </div>
                );
              
              default:
                // For custom features that don't correspond to specific components
                return null;
            }
          })}
          
          {/* Show components for unconfigured features or when no features are configured */}
          {(!bannerFeatures.length || loadingFeatures) && (
            <>
              <UrlShortenerWithDomains approvedDomains={approvedDomains} />
              <TempMailbox />
              {/* Navigation and other sections... */}
            </>
          )}
          
          {/* About Section - 仅在用户未登录时显示 */}
          {!isAuthenticated && (
            <AboutSection />
          )}

          {/* CTA Section - 仅在用户未登录时显示 */}
          {!isAuthenticated && (
            <CtaSection />
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
