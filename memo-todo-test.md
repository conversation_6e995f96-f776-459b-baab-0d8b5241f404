# 备忘录与待办功能测试计划

## 🔍 问题分析

### 已修复的问题
1. **MemoTodo.tsx第142行错误**: `Cannot read properties of undefined (reading 'toLowerCase')`
   - **原因**: `memo.content` 可能为 `undefined`
   - **修复**: 添加了安全检查 `if (!memo || !memo.content || typeof memo.content !== 'string')`

### 需要测试的功能

## 📋 备忘录功能测试

### 1. 创建备忘录
- [ ] 基本文本备忘录创建
- [ ] 带标签的备忘录创建
- [ ] 带提醒日期的备忘录创建
- [ ] 空内容验证（应该阻止创建）

### 2. 查看备忘录
- [ ] 备忘录列表显示
- [ ] 备忘录详情查看
- [ ] 标签筛选功能
- [ ] 搜索功能

### 3. 编辑备忘录
- [ ] 修改备忘录内容
- [ ] 修改标签
- [ ] 修改提醒日期
- [ ] 编辑后保存

### 4. 删除备忘录
- [ ] 单个备忘录删除
- [ ] 删除确认提示
- [ ] 删除后列表更新

## 📝 待办事项功能测试

### 1. 创建待办
- [ ] 基本待办事项创建
- [ ] 带截止日期的待办创建
- [ ] 带优先级的待办创建
- [ ] 空标题验证（应该阻止创建）

### 2. 查看待办
- [ ] 待办列表显示
- [ ] 待办详情查看
- [ ] 优先级筛选
- [ ] 完成状态筛选
- [ ] 截止日期筛选

### 3. 编辑待办
- [ ] 修改待办标题
- [ ] 修改截止日期
- [ ] 修改优先级
- [ ] 编辑后保存

### 4. 待办状态管理
- [ ] 标记为完成
- [ ] 标记为未完成
- [ ] 状态切换

### 5. 删除待办
- [ ] 单个待办删除
- [ ] 删除确认提示
- [ ] 删除后列表更新

## 🔧 API接口测试

### 备忘录API
- [ ] `POST /memos` - 创建备忘录
- [ ] `GET /memos` - 获取备忘录列表
- [ ] `PUT /memos/:id` - 更新备忘录
- [ ] `DELETE /memos/:id` - 删除备忘录

### 待办API
- [ ] `POST /todos` - 创建待办
- [ ] `GET /todos` - 获取待办列表
- [ ] `PUT /todos/:id` - 更新待办
- [ ] `DELETE /todos/:id` - 删除待办

## 🐛 已知问题和修复

### 1. 临时邮箱删除交互优化 ✅
- **问题**: 使用原生 `window.confirm()` 提示
- **修复**: 替换为 `AlertDialog` 组件
- **文件**: `src/pages/index/TempMailbox.tsx`

### 2. 备忘录内容安全检查 ✅
- **问题**: `memo.content.toLowerCase()` 报错
- **修复**: 添加安全检查
- **文件**: `src/pages/MemoTodo.tsx` 第142行

## 🧪 测试步骤

### 准备工作
1. 确保后端服务运行在 `http://localhost:8081`
2. 确保前端服务运行在 `http://localhost:3000`
3. 使用超级管理员账号登录

### 测试流程
1. **登录验证**
   - 访问 `/memo-todo` 页面
   - 确认需要登录才能访问

2. **备忘录测试**
   - 创建新备忘录
   - 编辑现有备忘录
   - 删除备忘录
   - 测试搜索和筛选

3. **待办事项测试**
   - 创建新待办
   - 编辑现有待办
   - 切换完成状态
   - 删除待办
   - 测试筛选功能

4. **错误处理测试**
   - 网络错误处理
   - 权限错误处理
   - 数据验证错误处理

## 🎯 预期结果

### 成功标准
- [ ] 所有CRUD操作正常工作
- [ ] 没有控制台错误
- [ ] UI响应流畅
- [ ] 数据持久化正确
- [ ] 错误处理得当

### 性能标准
- [ ] 页面加载时间 < 2秒
- [ ] 操作响应时间 < 1秒
- [ ] 无内存泄漏
- [ ] 无重复API调用
